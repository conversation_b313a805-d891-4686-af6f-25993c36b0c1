#ifndef ESP_ARDUINO_SHIM_H
#define ESP_ARDUINO_SHIM_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <string.h> // For memcpy
#include <stdio.h>  // For FILE*
#include <stdlib.h> // For malloc, free, random

// ESP-IDF specific headers
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_timer.h"
#include "esp_random.h"
#include "esp_heap_caps.h" // For heap_caps_malloc

// Define NOP for ESP32
#define NOP() asm volatile ("nop")

// Arduino data types
typedef uint8_t byte;
typedef bool boolean;
// typedef unsigned long word; // Typically uint16_t on Arduino, but can conflict. Let's see if it's needed.

// Arduino constants
#ifndef PROGMEM
#define PROGMEM
#endif
#ifndef IRAM_ATTR
#define IRAM_ATTR
#endif

// Timing functions
static inline unsigned long millis(void) {
    return (unsigned long)(esp_timer_get_time() / 1000ULL);
}

static inline unsigned long micros(void) {
    return (unsigned long)(esp_timer_get_time());
}

static inline void delay(uint32_t ms) {
    if (ms == 0) {
        // taskYIELD or vTaskDelay(0) can be used for yielding,
        // but vTaskDelay(1) is safer if a short actual delay is acceptable.
        vTaskDelay(pdMS_TO_TICKS(1));
        return;
    }
    vTaskDelay(pdMS_TO_TICKS(ms));
}

static inline void delayMicroseconds(unsigned int us) {
    uint64_t m = (uint64_t)esp_timer_get_time();
    if (us) {
        uint64_t e = (m + us);
        if (m > e) { //overflow
            while ((uint64_t)esp_timer_get_time() > e) {
                NOP();
            }
        }
        while ((uint64_t)esp_timer_get_time() < e) {
            NOP();
        }
    }
}

// PROGMEM data reading functions
// In ESP-IDF, data marked with PROGMEM_ATTR (which PROGMEM can be defined to)
// is typically in flash and can be accessed directly via pointers.
// So, these functions might just dereference the pointer.
// Ensure that the address is valid and points to flash.
#define pgm_read_byte(addr) (*(const unsigned char *)(addr))
#define pgm_read_word(addr) (*(const unsigned short *)(addr))
#define pgm_read_dword(addr) (*(const unsigned long *)(addr))
#define pgm_read_float(addr) (*(const float *)(addr))

// For compatibility with pgm_read_byte_near (often used in Arduino libraries)
#define pgm_read_byte_near(addr) pgm_read_byte(addr)
#define pgm_read_word_near(addr) pgm_read_word(addr)

// memcpy_P for PROGMEM
// Again, on ESP32, this can often be a direct memcpy if src is a pointer to flash.
#define memcpy_P(dest, src, num) memcpy(dest, src, num)

// Random numbers
// Arduino's random(max) gives [0, max-1]
// Arduino's random(min, max) gives [min, max-1]
// Note: Renamed to avoid conflict with stdlib random()
static inline long arduino_random(long howsmall, long howbig) {
    if (howsmall >= howbig) {
        return howsmall;
    }
    long diff = howbig - howsmall;
    return howsmall + (esp_random() % diff);
}

static inline long arduino_random(long howbig) {
    if (howbig <= 0) {
        return 0;
    }
    return esp_random() % howbig;
}

// Provide macros for compatibility if needed
// Note: These macros may conflict with stdlib random(), use arduino_random() directly if needed

// Serial object stub (if absolutely necessary, better to replace with ESP_LOG)
// This is a very minimal stub. Full Serial emulation is complex and often not needed.
#if 0 // Enable if truly needed and can't replace with ESP_LOG easily
typedef struct {
    // Add methods if a particular Serial function is used by the library
    // e.g., void (*println_str)(const char* s);
} HardwareSerial;
extern HardwareSerial Serial; // Declaration
#endif


// File system related - this is the most complex part to shim.
// The AnimatedGIF library uses callback functions for file operations,
// which is good. We will provide ESP-IDF specific implementations for these
// callbacks in the main application code.
// So, we don't need to fully shim Arduino's FS.h or File class here.
// We just need to ensure any related types used directly in AnimatedGIF.h/cpp are covered.

// If the library directly uses `File` type in its public API or internal structures
// that cannot be avoided, we might need a minimal placeholder.
// Let's assume for now the callbacks are sufficient.

#ifdef __cplusplus
} // extern "C"
#endif

// C++ specific shims
#ifdef __cplusplus
// Minimal String class stub (if needed)
// It's highly preferable to remove String usage or replace it with const char* / std::string.
class String {
public:
    String(const char *cstr = "") : _cstr(cstr ? strdup(cstr) : strdup("")) {}
    String(const String &other) : _cstr(other._cstr ? strdup(other._cstr) : strdup("")) {}
    ~String() { if (_cstr) free(_cstr); }
    String& operator=(const String& rhs) {
        if (this == &rhs) return *this;
        if (_cstr) free(_cstr);
        _cstr = rhs._cstr ? strdup(rhs._cstr) : strdup("");
        return *this;
    }
    const char* c_str() const { return _cstr; }
    // Add other methods if the library uses them.
private:
    char *_cstr;
};

#endif // __cplusplus

#endif // ESP_ARDUINO_SHIM_H
