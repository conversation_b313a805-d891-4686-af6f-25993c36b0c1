#include "eye_gif_draw_display.h"
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

static const char* TAG = "EyeGifDrawExample";

/**
 * @brief EyeGifDrawDisplay 使用示例
 *
 * 展示如何使用基于AnimatedGIF的GIF显示类，包含10帧PSRAM缓存和DMA渲染
 */
void eye_gif_draw_example() {
    ESP_LOGI(TAG, "=== EyeGifDrawDisplay with AnimatedGIF Usage Example ===");

    // 创建双屏GIF显示器（基于AnimatedGIF，带10帧缓存和DMA渲染）
    DisplayFonts fonts; // 假设已初始化
    EyeGifDrawDisplay display(240, 240, 0, 0, false, false, false, fonts);

    ESP_LOGI(TAG, "EyeGifDrawDisplay created with:");
    ESP_LOGI(TAG, "  - AnimatedGIF decoder");
    ESP_LOGI(TAG, "  - 10-frame PSRAM cache per screen");
    ESP_LOGI(TAG, "  - DMA rendering capability");
    ESP_LOGI(TAG, "  - Turbo mode (if memory available)");

    // 调试显示信息
    display.DebugDisplayInfo();

    // 设置不同屏幕显示不同表情
    ESP_LOGI(TAG, "Setting different emotions on each screen");
    display.SetEmotionOnScreen(1, "happy");    // 左屏显示开心表情
    display.SetEmotionOnScreen(2, "surprised"); // 右屏显示惊讶表情

    // 等待一段时间观察效果和缓存建立
    ESP_LOGI(TAG, "Waiting for frame cache to build up...");
    vTaskDelay(pdMS_TO_TICKS(5000));

    // 同步两个屏幕显示相同表情
    ESP_LOGI(TAG, "Setting synchronized emotion on both screens");
    display.SetSyncEmotion("heart_beat");

    vTaskDelay(pdMS_TO_TICKS(3000));

    // 设置双屏不同表情
    ESP_LOGI(TAG, "Setting dual emotions");
    display.SetDualEmotion("purple_left", "purple_right");

    vTaskDelay(pdMS_TO_TICKS(3000));

    // 通过基类接口设置表情（会同步到两屏）
    ESP_LOGI(TAG, "Setting emotion through base class interface");
    display.SetEmotion("sad");

    vTaskDelay(pdMS_TO_TICKS(3000));

    // 测试图标映射
    ESP_LOGI(TAG, "Testing icon mapping");
    display.SetIcon(FONT_AWESOME_DOWNLOAD); // 应该显示loading表情

    vTaskDelay(pdMS_TO_TICKS(2000));

    display.SetIcon(FONT_AWESOME_MUSIC); // 应该显示listening表情

    vTaskDelay(pdMS_TO_TICKS(2000));

    ESP_LOGI(TAG, "=== EyeGifDrawDisplay with AnimatedGIF Example Completed ===");
}

/**
 * @brief 性能测试示例 - 测试AnimatedGIF和帧缓存性能
 */
void eye_gif_draw_performance_test() {
    ESP_LOGI(TAG, "=== EyeGifDrawDisplay Performance Test with AnimatedGIF ===");

    DisplayFonts fonts;
    EyeGifDrawDisplay display(240, 240, 0, 0, false, false, false, fonts);

    // 快速切换表情测试
    const char* emotions[] = {
        "happy", "sad", "surprised", "angry", "thinking",
        "heart_beat", "purple_left", "purple_right"
    };
    const int emotion_count = sizeof(emotions) / sizeof(emotions[0]);

    ESP_LOGI(TAG, "Starting rapid emotion switching test");
    ESP_LOGI(TAG, "This test will demonstrate:");
    ESP_LOGI(TAG, "  - Initial decode time (cache miss)");
    ESP_LOGI(TAG, "  - Subsequent playback speed (cache hit)");
    ESP_LOGI(TAG, "  - Frame cache effectiveness");

    for (int cycle = 0; cycle < 3; cycle++) {
        ESP_LOGI(TAG, "Cycle %d/3 - %s", cycle + 1,
                 cycle == 0 ? "Building cache" : "Using cache");

        uint32_t cycle_start = esp_timer_get_time() / 1000;

        for (int i = 0; i < emotion_count; i++) {
            uint32_t emotion_start = esp_timer_get_time() / 1000;

            ESP_LOGI(TAG, "Setting emotion: %s", emotions[i]);
            display.SetEmotion(emotions[i]);

            // 让GIF播放一段时间以建立缓存
            vTaskDelay(pdMS_TO_TICKS(2000));

            uint32_t emotion_time = esp_timer_get_time() / 1000 - emotion_start;
            ESP_LOGI(TAG, "Emotion switch completed in %lu ms", emotion_time);
        }

        uint32_t cycle_time = esp_timer_get_time() / 1000 - cycle_start;
        ESP_LOGI(TAG, "Cycle %d completed in %lu ms", cycle + 1, cycle_time);

        if (cycle == 0) {
            ESP_LOGI(TAG, "Cache should now be populated for subsequent cycles");
        }
    }

    ESP_LOGI(TAG, "=== Performance Test Completed ===");
    ESP_LOGI(TAG, "Expected results:");
    ESP_LOGI(TAG, "  - Cycle 1: Slower (cache building)");
    ESP_LOGI(TAG, "  - Cycle 2-3: Faster (cache hits)");
}

/**
 * @brief 双屏独立控制测试
 */
void eye_gif_draw_dual_screen_test() {
    ESP_LOGI(TAG, "=== EyeGifDrawDisplay Dual Screen Test ===");
    
    DisplayFonts fonts;
    EyeGifDrawDisplay display(240, 240, 0, 0, false, false, false, fonts);
    
    // 测试双屏独立控制
    const char* left_emotions[] = {"happy", "sad", "surprised", "thinking"};
    const char* right_emotions[] = {"angry", "heart_beat", "purple_right", "cute"};
    const int test_count = 4;
    
    ESP_LOGI(TAG, "Testing independent dual screen control");
    
    for (int i = 0; i < test_count; i++) {
        ESP_LOGI(TAG, "Test %d/%d: Left='%s', Right='%s'", 
                 i + 1, test_count, left_emotions[i], right_emotions[i]);
        
        display.SetDualEmotion(left_emotions[i], right_emotions[i]);
        
        vTaskDelay(pdMS_TO_TICKS(2000));
    }
    
    // 测试单屏更新
    ESP_LOGI(TAG, "Testing single screen updates");
    
    display.SetEmotionOnScreen(1, "happy");
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    display.SetEmotionOnScreen(2, "sad");
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    display.SetEmotionOnScreen(1, "surprised");
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    display.SetEmotionOnScreen(2, "angry");
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 最后同步显示
    ESP_LOGI(TAG, "Final synchronization");
    display.SetSyncEmotion("staticstate");
    
    ESP_LOGI(TAG, "=== Dual Screen Test Completed ===");
}

/**
 * @brief 主测试函数
 */
void run_eye_gif_draw_tests() {
    ESP_LOGI(TAG, "Starting EyeGifDrawDisplay tests");
    
    // 基本使用示例
    eye_gif_draw_example();
    
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 性能测试
    eye_gif_draw_performance_test();
    
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 双屏测试
    eye_gif_draw_dual_screen_test();
    
    ESP_LOGI(TAG, "All EyeGifDrawDisplay tests completed");
}
