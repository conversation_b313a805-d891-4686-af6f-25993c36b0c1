#pragma once

#include <esp_lcd_panel_interface.h>
#include <esp_lcd_panel_io.h>
#include <esp_err.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/semphr.h>
#include <freertos/queue.h>
#include "display/lcd_display.h"
#include "gif_config.h"
#include "eye_gif_resources.h"
#include "gif_decoder.h"

/**
 * @brief 魔眼GIF绘制显示类
 *
 * 专为 doit-esp32s3-eye-6824 开发板设计的双屏 GIF 显示类
 * 特点：
 * - 移除LVGL依赖，使用手动GIF解码
 * - 支持双屏独立控制，显示不同的 GIF 动画
 * - 直接调用LCD面板的draw_bitmap函数进行渲染
 * - 使用gif_decoder手动解码GIF帧
 * - 全屏显示 240x240 GIF 动画
 * - 继承 Display 基类，完全自定义双屏逻辑
 */
class EyeGifDrawDisplay : public Display {
public:
    EyeGifDrawDisplay(int width, int height, int offset_x, int offset_y, bool mirror_x,
                      bool mirror_y, bool swap_xy, DisplayFonts fonts);

    virtual ~EyeGifDrawDisplay();

    // 重写基类方法 - 只处理表情显示
    virtual void SetEmotion(const char* emotion) override;
    // 重写基类方法 - 空实现，不显示任何文字
    virtual void SetChatMessage(const char* role, const char* content) override;
    virtual void SetIcon(const char* icon) override;
    virtual void SetStatus(const char* status) override;
    virtual void SetTheme(const std::string& theme_name) override;

    // 新的双屏控制接口
    void SetEmotionOnScreen(uint8_t screen_id, const char* emotion);
    void SetDualEmotion(const char* emotion1, const char* emotion2);
    void SetSyncEmotion(const char* emotion); // 两屏显示相同表情

    // 重写基类的锁机制
    virtual bool Lock(int timeout_ms = 100) override;
    virtual void Unlock() override;

    // 调试函数
    void DebugDisplayInfo();

private:
    // 表情映射结构
    struct EmotionMap {
        const char* name;
        const lv_img_dsc_t* gif;
    };

    // GIF播放状态结构
    struct GifPlayState {
        gif_decoder_t* decoder;
        const lv_img_dsc_t* gif_resource;
        uint32_t current_frame;
        uint32_t total_frames;
        uint32_t last_update_time;
        uint32_t frame_delay;
        uint16_t* frame_buffer;  // RGB565帧缓冲区
        bool is_playing;
        bool needs_update;
    };

    // GIF渲染消息结构
    struct GifRenderMessage {
        uint8_t screen_id;  // 1 or 2
        const lv_img_dsc_t* gif_resource;
        bool sync_both_screens;
    };

    // 静态表情映射表
    static const EmotionMap emotion_maps_[];

    // 显示参数
    int width_, height_;
    int offset_x_, offset_y_;
    bool mirror_x_, mirror_y_, swap_xy_;
    DisplayFonts fonts_;

    // 双屏LCD面板句柄
    esp_lcd_panel_io_handle_t panel_io1_ = nullptr;
    esp_lcd_panel_handle_t panel1_ = nullptr;
    esp_lcd_panel_io_handle_t panel_io2_ = nullptr;
    esp_lcd_panel_handle_t panel2_ = nullptr;

    // 双屏GIF播放状态
    GifPlayState gif_state1_;  // 屏幕1的GIF状态
    GifPlayState gif_state2_;  // 屏幕2的GIF状态

    // 互斥锁
    SemaphoreHandle_t mutex_ = nullptr;

    // GIF渲染任务
    TaskHandle_t render_task_handle_ = nullptr;
    QueueHandle_t render_queue_ = nullptr;

    // 初始化方法
    void InitializePanels();
    void InitializeGifStates();

    // GIF播放控制
    void StartGifRenderTask();
    void StopGifRenderTask();
    static void GifRenderTask(void* parameter);

    // GIF解码和渲染
    esp_err_t LoadGifOnScreen(uint8_t screen_id, const lv_img_dsc_t* gif_resource);
    esp_err_t UpdateGifFrame(uint8_t screen_id);
    esp_err_t RenderFrameToScreen(uint8_t screen_id, const uint16_t* frame_buffer);

    // 辅助方法
    const lv_img_dsc_t* GetGifResource(const char* emotion);
    esp_lcd_panel_handle_t GetPanelHandle(uint8_t screen_id);
    GifPlayState* GetGifState(uint8_t screen_id);
    void CleanupGifState(GifPlayState* state);
    uint32_t GetCurrentTimeMs();

    // 内存管理
    uint16_t* AllocateFrameBuffer();
    void FreeFrameBuffer(uint16_t* buffer);
};
