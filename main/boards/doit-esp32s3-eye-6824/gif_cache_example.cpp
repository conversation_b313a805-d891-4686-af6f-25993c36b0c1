/**
 * @file gif_cache_example.cpp
 * @brief GIF帧缓存优化方案使用示例
 * 
 * 本示例展示如何使用新的GIF帧缓存系统来实现高性能的GIF播放
 */

#include "eye_gif_display.h"
#include "gif_cache_test.h"
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

static const char* TAG = "GifCacheExample";

/**
 * @brief 基本使用示例
 */
void basic_usage_example() {
    ESP_LOGI(TAG, "=== Basic Usage Example ===");
    
    // 创建双屏GIF显示器
    DisplayFonts fonts; // 假设已初始化
    EyeGifDisplay display(240, 240, 0, 0, false, false, false, fonts);
    
    ESP_LOGI(TAG, "GIF display created with frame cache enabled");
    
    // 设置不同屏幕显示不同表情
    display.SetEmotionOnScreen(1, "happy");    // 左屏显示开心表情
    display.SetEmotionOnScreen(2, "surprised"); // 右屏显示惊讶表情
    
    // 等待一段时间观察效果
    vTaskDelay(pdMS_TO_TICKS(5000));
    
    // 同步两个屏幕显示相同表情
    display.SetSyncEmotion("heart_beat");
    
    vTaskDelay(pdMS_TO_TICKS(3000));
    
#if GIF_FRAME_CACHE_ENABLED
    // 打印缓存统计信息
    display.PrintFrameCacheStats();
#endif
    
    ESP_LOGI(TAG, "Basic usage example completed");
}

/**
 * @brief 性能测试示例
 */
void performance_test_example() {
    ESP_LOGI(TAG, "=== Performance Test Example ===");
    
    // 运行完整的测试套件
    esp_err_t result = gif_cache_run_full_test_suite();
    
    if (result == ESP_OK) {
        ESP_LOGI(TAG, "✅ All performance tests passed!");
        ESP_LOGI(TAG, "The GIF cache system is working optimally");
    } else {
        ESP_LOGE(TAG, "❌ Some performance tests failed");
        ESP_LOGI(TAG, "Check the logs above for detailed information");
    }
}

/**
 * @brief 表情切换演示
 */
void emotion_switching_demo() {
    ESP_LOGI(TAG, "=== Emotion Switching Demo ===");
    
    DisplayFonts fonts;
    EyeGifDisplay display(240, 240, 0, 0, false, false, false, fonts);
    
    // 表情列表
    const char* emotions[] = {
        "happy", "sad", "angry", "surprised", "thinking", 
        "heart_beat", "moving", "cute", "purple_left", "purple_right"
    };
    const int emotion_count = sizeof(emotions) / sizeof(emotions[0]);
    
    ESP_LOGI(TAG, "Starting emotion switching demo with %d emotions", emotion_count);
    
    for (int cycle = 0; cycle < 3; cycle++) {
        ESP_LOGI(TAG, "--- Cycle %d ---", cycle + 1);
        
        for (int i = 0; i < emotion_count; i++) {
            ESP_LOGI(TAG, "Displaying emotion: %s", emotions[i]);
            
            if (i % 2 == 0) {
                // 偶数索引：双屏同步显示
                display.SetSyncEmotion(emotions[i]);
            } else {
                // 奇数索引：双屏显示不同表情
                display.SetEmotionOnScreen(1, emotions[i]);
                display.SetEmotionOnScreen(2, emotions[(i + 1) % emotion_count]);
            }
            
            // 每个表情显示2秒
            vTaskDelay(pdMS_TO_TICKS(2000));
        }
    }
    
#if GIF_FRAME_CACHE_ENABLED
    // 显示最终的缓存统计
    ESP_LOGI(TAG, "Final cache statistics:");
    display.PrintFrameCacheStats();
#endif
    
    ESP_LOGI(TAG, "Emotion switching demo completed");
}

/**
 * @brief 内存使用监控示例
 */
void memory_monitoring_example() {
    ESP_LOGI(TAG, "=== Memory Monitoring Example ===");
    
    // 记录初始内存状态
    size_t initial_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t initial_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    ESP_LOGI(TAG, "Initial memory state:");
    ESP_LOGI(TAG, "  Internal RAM: %zu KB", initial_internal / 1024);
    ESP_LOGI(TAG, "  PSRAM: %zu KB", initial_psram / 1024);
    
    {
        // 创建GIF显示器 (在作用域内)
        DisplayFonts fonts;
        EyeGifDisplay display(240, 240, 0, 0, false, false, false, fonts);
        
        // 记录创建后的内存状态
        size_t after_create_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
        size_t after_create_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
        
        ESP_LOGI(TAG, "After creating display:");
        ESP_LOGI(TAG, "  Internal RAM: %zu KB (used: %zu KB)", 
                 after_create_internal / 1024, 
                 (initial_internal - after_create_internal) / 1024);
        ESP_LOGI(TAG, "  PSRAM: %zu KB (used: %zu KB)", 
                 after_create_psram / 1024, 
                 (initial_psram - after_create_psram) / 1024);
        
        // 运行一些GIF操作
        display.SetSyncEmotion("happy");
        vTaskDelay(pdMS_TO_TICKS(2000));
        
        display.SetEmotionOnScreen(1, "sad");
        display.SetEmotionOnScreen(2, "angry");
        vTaskDelay(pdMS_TO_TICKS(2000));
        
        // 记录运行后的内存状态
        size_t after_run_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
        size_t after_run_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
        
        ESP_LOGI(TAG, "After running operations:");
        ESP_LOGI(TAG, "  Internal RAM: %zu KB", after_run_internal / 1024);
        ESP_LOGI(TAG, "  PSRAM: %zu KB", after_run_psram / 1024);
        
#if GIF_FRAME_CACHE_ENABLED
        display.PrintFrameCacheStats();
#endif
    }
    
    // 对象销毁后检查内存释放
    vTaskDelay(pdMS_TO_TICKS(1000)); // 等待清理完成
    
    size_t final_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t final_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    ESP_LOGI(TAG, "After cleanup:");
    ESP_LOGI(TAG, "  Internal RAM: %zu KB (recovered: %zu KB)", 
             final_internal / 1024,
             (final_internal - initial_internal) / 1024);
    ESP_LOGI(TAG, "  PSRAM: %zu KB (recovered: %zu KB)", 
             final_psram / 1024,
             (final_psram - initial_psram) / 1024);
    
    ESP_LOGI(TAG, "Memory monitoring example completed");
}

/**
 * @brief 主示例函数
 */
extern "C" void gif_cache_example_main() {
    ESP_LOGI(TAG, "Starting GIF Cache Optimization Examples");
    
    // 1. 基本使用示例
    basic_usage_example();
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 2. 性能测试示例
    performance_test_example();
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 3. 表情切换演示
    emotion_switching_demo();
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 4. 内存监控示例
    memory_monitoring_example();
    
    ESP_LOGI(TAG, "All examples completed successfully!");
    ESP_LOGI(TAG, "GIF cache optimization is ready for production use.");
}

/**
 * @brief 创建示例任务
 */
extern "C" void create_gif_cache_example_task() {
    xTaskCreatePinnedToCore(
        [](void* param) {
            gif_cache_example_main();
            vTaskDelete(NULL);
        },
        "gif_cache_example",
        8192,  // 8KB堆栈
        NULL,
        2,     // 低优先级
        NULL,
        1      // 运行在核心1
    );
}
