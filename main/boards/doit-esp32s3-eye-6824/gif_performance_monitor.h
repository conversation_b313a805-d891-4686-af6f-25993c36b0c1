#pragma once

#include <stdint.h>
#include <esp_err.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/semphr.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 性能监控数据结构
 */
typedef struct {
    // 帧率统计
    uint32_t frame_count;               // 总帧数
    uint32_t frame_display_count;       // 显示帧数
    uint32_t frame_skip_count;          // 跳帧数
    float current_fps;                  // 当前帧率
    float average_fps;                  // 平均帧率
    float target_fps;                   // 目标帧率
    
    // 时间统计
    uint32_t start_time_ms;             // 开始时间
    uint32_t last_frame_time_ms;        // 上一帧时间
    uint32_t total_runtime_ms;          // 总运行时间
    
    // 解码性能
    uint32_t decode_time_total_ms;      // 总解码时间
    uint32_t decode_count;              // 解码次数
    float avg_decode_time_ms;           // 平均解码时间
    uint32_t max_decode_time_ms;        // 最大解码时间
    uint32_t min_decode_time_ms;        // 最小解码时间
    
    // 缓存性能
    uint32_t cache_hits;                // 缓存命中
    uint32_t cache_misses;              // 缓存未命中
    float cache_hit_rate;               // 缓存命中率
    
    // 内存使用
    size_t memory_used_internal;        // 内部RAM使用量
    size_t memory_used_psram;           // PSRAM使用量
    size_t memory_peak_internal;        // 内部RAM峰值
    size_t memory_peak_psram;           // PSRAM峰值
    
    // 系统负载
    uint32_t cpu_usage_percent;         // CPU使用率
    uint32_t free_heap_size;            // 空闲堆大小
    uint32_t min_free_heap_size;        // 最小空闲堆大小
} gif_performance_stats_t;

/**
 * @brief 性能监控器结构
 */
typedef struct {
    gif_performance_stats_t stats;      // 性能统计数据
    SemaphoreHandle_t mutex;            // 互斥锁
    TaskHandle_t monitor_task_handle;   // 监控任务句柄
    bool monitoring_enabled;            // 是否启用监控
    bool auto_print_enabled;            // 是否自动打印统计
    uint32_t print_interval_ms;         // 打印间隔
} gif_performance_monitor_t;

/**
 * @brief 创建性能监控器
 * 
 * @param target_fps 目标帧率
 * @return gif_performance_monitor_t* 监控器指针，失败返回NULL
 */
gif_performance_monitor_t* gif_performance_monitor_create(float target_fps);

/**
 * @brief 销毁性能监控器
 * 
 * @param monitor 监控器指针
 */
void gif_performance_monitor_destroy(gif_performance_monitor_t* monitor);

/**
 * @brief 启动性能监控
 * 
 * @param monitor 监控器指针
 * @param auto_print 是否自动打印统计
 * @param print_interval_ms 打印间隔(毫秒)
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_performance_monitor_start(gif_performance_monitor_t* monitor, 
                                       bool auto_print, uint32_t print_interval_ms);

/**
 * @brief 停止性能监控
 * 
 * @param monitor 监控器指针
 */
void gif_performance_monitor_stop(gif_performance_monitor_t* monitor);

/**
 * @brief 记录帧显示事件
 * 
 * @param monitor 监控器指针
 */
void gif_performance_monitor_record_frame(gif_performance_monitor_t* monitor);

/**
 * @brief 记录解码时间
 * 
 * @param monitor 监控器指针
 * @param decode_time_ms 解码时间(毫秒)
 */
void gif_performance_monitor_record_decode_time(gif_performance_monitor_t* monitor, 
                                               uint32_t decode_time_ms);

/**
 * @brief 记录缓存命中
 * 
 * @param monitor 监控器指针
 * @param hit 是否命中
 */
void gif_performance_monitor_record_cache_access(gif_performance_monitor_t* monitor, 
                                                 bool hit);

/**
 * @brief 更新内存使用情况
 * 
 * @param monitor 监控器指针
 */
void gif_performance_monitor_update_memory(gif_performance_monitor_t* monitor);

/**
 * @brief 获取性能统计数据
 * 
 * @param monitor 监控器指针
 * @return const gif_performance_stats_t* 统计数据指针
 */
const gif_performance_stats_t* gif_performance_monitor_get_stats(gif_performance_monitor_t* monitor);

/**
 * @brief 打印性能统计报告
 * 
 * @param monitor 监控器指针
 */
void gif_performance_monitor_print_report(gif_performance_monitor_t* monitor);

/**
 * @brief 重置性能统计数据
 * 
 * @param monitor 监控器指针
 */
void gif_performance_monitor_reset(gif_performance_monitor_t* monitor);

/**
 * @brief 检查是否达到目标性能
 * 
 * @param monitor 监控器指针
 * @return bool true表示达到目标性能
 */
bool gif_performance_monitor_check_target_performance(gif_performance_monitor_t* monitor);

#ifdef __cplusplus
}
#endif
