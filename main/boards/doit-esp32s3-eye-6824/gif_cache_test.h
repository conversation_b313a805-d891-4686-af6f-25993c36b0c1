#pragma once

#include <esp_err.h>
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 测试结果结构
 */
typedef struct {
    bool test_passed;                   // 测试是否通过
    float achieved_fps;                 // 实际达到的帧率
    float cache_hit_rate;               // 缓存命中率
    uint32_t avg_decode_time_ms;        // 平均解码时间
    size_t memory_used_psram;           // PSRAM使用量
    size_t memory_used_internal;        // 内部RAM使用量
    uint32_t test_duration_ms;          // 测试持续时间
    char error_message[256];            // 错误信息
} gif_cache_test_result_t;

/**
 * @brief 运行GIF缓存性能测试
 * 
 * @param test_duration_ms 测试持续时间(毫秒)
 * @param target_fps 目标帧率
 * @param result 测试结果输出
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_cache_run_performance_test(uint32_t test_duration_ms, float target_fps, 
                                        gif_cache_test_result_t* result);

/**
 * @brief 运行内存压力测试
 * 
 * @param result 测试结果输出
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_cache_run_memory_stress_test(gif_cache_test_result_t* result);

/**
 * @brief 运行缓存效率测试
 * 
 * @param result 测试结果输出
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_cache_run_cache_efficiency_test(gif_cache_test_result_t* result);

/**
 * @brief 运行完整的测试套件
 * 
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_cache_run_full_test_suite(void);

/**
 * @brief 打印测试结果
 * 
 * @param result 测试结果
 * @param test_name 测试名称
 */
void gif_cache_print_test_result(const gif_cache_test_result_t* result, const char* test_name);

#ifdef __cplusplus
}
#endif
