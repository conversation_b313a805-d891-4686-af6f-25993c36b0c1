// 简单的编译测试文件
#include "gif_frame_cache.h"
#include "gif_decoder.h"
#include "gif_performance_monitor.h"
#include "gif_cache_test.h"
#include "eye_gif_resources.h"
#include <esp_log.h>

static const char* TAG = "CompileTest";

// 测试函数，验证所有头文件和函数声明是否正确
void test_compile_check(void) {
    ESP_LOGI(TAG, "Starting compile test");

    // 测试GIF帧缓存
    gif_frame_cache_t* cache = gif_frame_cache_create((const lv_img_dsc_t*)&staticstate, 240, 240, 10, true);
    if (cache) {
        ESP_LOGI(TAG, "Frame cache created successfully");
        gif_frame_cache_destroy(cache);
    }

    // 测试GIF解码器
    gif_decoder_t* decoder = gif_decoder_create((const lv_img_dsc_t*)&staticstate);
    if (decoder) {
        ESP_LOGI(TAG, "GIF decoder created successfully");
        gif_decoder_destroy(decoder);
    }

    // 测试性能监控器
    gif_performance_monitor_t* monitor = gif_performance_monitor_create(20.0f);
    if (monitor) {
        ESP_LOGI(TAG, "Performance monitor created successfully");
        gif_performance_monitor_destroy(monitor);
    }

    // 测试套件
    gif_cache_test_result_t result;
    esp_err_t ret = gif_cache_run_performance_test(1000, 20.0f, &result);
    ESP_LOGI(TAG, "Test suite result: %s", ret == ESP_OK ? "OK" : "FAILED");

    ESP_LOGI(TAG, "Compile test completed");
}
