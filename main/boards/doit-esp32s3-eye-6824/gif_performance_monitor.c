#include "gif_performance_monitor.h"
#include <esp_log.h>
#include <esp_heap_caps.h>
#include <esp_system.h>
#include <sys/time.h>
#include <string.h>
#include <stdlib.h>

static const char* TAG = "GifPerfMonitor";

// 获取当前时间戳 (毫秒)
static uint32_t get_timestamp_ms(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (tv.tv_sec * 1000) + (tv.tv_usec / 1000);
}

// 性能监控任务
static void performance_monitor_task(void* parameter) {
    gif_performance_monitor_t* monitor = (gif_performance_monitor_t*)parameter;
    
    ESP_LOGI(TAG, "Performance monitor task started");
    
    while (monitor->monitoring_enabled) {
        // 更新内存使用情况
        gif_performance_monitor_update_memory(monitor);
        
        // 自动打印统计信息
        if (monitor->auto_print_enabled) {
            gif_performance_monitor_print_report(monitor);
        }
        
        // 等待下一个监控周期
        vTaskDelay(pdMS_TO_TICKS(monitor->print_interval_ms));
    }
    
    ESP_LOGI(TAG, "Performance monitor task stopped");
    vTaskDelete(NULL);
}

gif_performance_monitor_t* gif_performance_monitor_create(float target_fps) {
    gif_performance_monitor_t* monitor = malloc(sizeof(gif_performance_monitor_t));
    if (!monitor) {
        ESP_LOGE(TAG, "Failed to allocate performance monitor");
        return NULL;
    }
    
    memset(monitor, 0, sizeof(gif_performance_monitor_t));
    
    // 创建互斥锁
    monitor->mutex = xSemaphoreCreateMutex();
    if (!monitor->mutex) {
        ESP_LOGE(TAG, "Failed to create mutex");
        free(monitor);
        return NULL;
    }
    
    // 初始化统计数据
    monitor->stats.target_fps = target_fps;
    monitor->stats.start_time_ms = get_timestamp_ms();
    monitor->stats.min_decode_time_ms = UINT32_MAX;
    monitor->monitoring_enabled = false;
    monitor->auto_print_enabled = false;
    monitor->print_interval_ms = 5000; // 默认5秒
    
    ESP_LOGI(TAG, "Performance monitor created with target FPS: %.1f", target_fps);
    return monitor;
}

void gif_performance_monitor_destroy(gif_performance_monitor_t* monitor) {
    if (!monitor) {
        return;
    }
    
    // 停止监控
    gif_performance_monitor_stop(monitor);
    
    // 清理互斥锁
    if (monitor->mutex) {
        vSemaphoreDelete(monitor->mutex);
    }
    
    free(monitor);
    ESP_LOGI(TAG, "Performance monitor destroyed");
}

esp_err_t gif_performance_monitor_start(gif_performance_monitor_t* monitor, 
                                       bool auto_print, uint32_t print_interval_ms) {
    if (!monitor) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (monitor->monitoring_enabled) {
        ESP_LOGW(TAG, "Performance monitor already running");
        return ESP_OK;
    }
    
    monitor->monitoring_enabled = true;
    monitor->auto_print_enabled = auto_print;
    monitor->print_interval_ms = print_interval_ms;
    
    // 重置统计数据
    gif_performance_monitor_reset(monitor);
    
    // 创建监控任务
    BaseType_t result = xTaskCreatePinnedToCore(
        performance_monitor_task,
        "gif_perf_monitor",
        4096,
        monitor,
        2, // 低优先级
        &monitor->monitor_task_handle,
        0  // 运行在核心0
    );
    
    if (result != pdPASS) {
        ESP_LOGE(TAG, "Failed to create performance monitor task");
        monitor->monitoring_enabled = false;
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Performance monitor started (auto_print: %s, interval: %lu ms)",
             auto_print ? "enabled" : "disabled", print_interval_ms);
    return ESP_OK;
}

void gif_performance_monitor_stop(gif_performance_monitor_t* monitor) {
    if (!monitor || !monitor->monitoring_enabled) {
        return;
    }
    
    ESP_LOGI(TAG, "Stopping performance monitor");
    monitor->monitoring_enabled = false;
    
    // 等待任务结束
    if (monitor->monitor_task_handle) {
        vTaskDelay(pdMS_TO_TICKS(100));
        monitor->monitor_task_handle = NULL;
    }
}

void gif_performance_monitor_record_frame(gif_performance_monitor_t* monitor) {
    if (!monitor || !monitor->monitoring_enabled) {
        return;
    }
    
    if (xSemaphoreTake(monitor->mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        uint32_t current_time = get_timestamp_ms();
        
        monitor->stats.frame_count++;
        monitor->stats.frame_display_count++;
        
        // 计算帧率
        if (monitor->stats.last_frame_time_ms > 0) {
            uint32_t frame_interval = current_time - monitor->stats.last_frame_time_ms;
            if (frame_interval > 0) {
                monitor->stats.current_fps = 1000.0f / frame_interval;
            }
        }
        
        monitor->stats.last_frame_time_ms = current_time;
        monitor->stats.total_runtime_ms = current_time - monitor->stats.start_time_ms;
        
        // 计算平均帧率
        if (monitor->stats.total_runtime_ms > 0) {
            monitor->stats.average_fps = (float)monitor->stats.frame_display_count * 1000.0f / 
                                        monitor->stats.total_runtime_ms;
        }
        
        xSemaphoreGive(monitor->mutex);
    }
}

void gif_performance_monitor_record_decode_time(gif_performance_monitor_t* monitor, 
                                               uint32_t decode_time_ms) {
    if (!monitor || !monitor->monitoring_enabled) {
        return;
    }
    
    if (xSemaphoreTake(monitor->mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        monitor->stats.decode_count++;
        monitor->stats.decode_time_total_ms += decode_time_ms;
        monitor->stats.avg_decode_time_ms = (float)monitor->stats.decode_time_total_ms / 
                                           monitor->stats.decode_count;
        
        if (decode_time_ms > monitor->stats.max_decode_time_ms) {
            monitor->stats.max_decode_time_ms = decode_time_ms;
        }
        
        if (decode_time_ms < monitor->stats.min_decode_time_ms) {
            monitor->stats.min_decode_time_ms = decode_time_ms;
        }
        
        xSemaphoreGive(monitor->mutex);
    }
}

void gif_performance_monitor_record_cache_access(gif_performance_monitor_t* monitor, 
                                                 bool hit) {
    if (!monitor || !monitor->monitoring_enabled) {
        return;
    }
    
    if (xSemaphoreTake(monitor->mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        if (hit) {
            monitor->stats.cache_hits++;
        } else {
            monitor->stats.cache_misses++;
        }
        
        uint32_t total_accesses = monitor->stats.cache_hits + monitor->stats.cache_misses;
        if (total_accesses > 0) {
            monitor->stats.cache_hit_rate = (float)monitor->stats.cache_hits / total_accesses * 100.0f;
        }
        
        xSemaphoreGive(monitor->mutex);
    }
}

void gif_performance_monitor_update_memory(gif_performance_monitor_t* monitor) {
    if (!monitor) {
        return;
    }

    if (xSemaphoreTake(monitor->mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        // 获取当前内存使用情况
        size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
        size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
        size_t total_internal = heap_caps_get_total_size(MALLOC_CAP_INTERNAL);
        size_t total_psram = heap_caps_get_total_size(MALLOC_CAP_SPIRAM);

        monitor->stats.memory_used_internal = total_internal - free_internal;
        monitor->stats.memory_used_psram = total_psram - free_psram;

        // 更新峰值
        if (monitor->stats.memory_used_internal > monitor->stats.memory_peak_internal) {
            monitor->stats.memory_peak_internal = monitor->stats.memory_used_internal;
        }
        if (monitor->stats.memory_used_psram > monitor->stats.memory_peak_psram) {
            monitor->stats.memory_peak_psram = monitor->stats.memory_used_psram;
        }

        // 更新堆信息
        monitor->stats.free_heap_size = esp_get_free_heap_size();
        monitor->stats.min_free_heap_size = esp_get_minimum_free_heap_size();

        xSemaphoreGive(monitor->mutex);
    }
}

const gif_performance_stats_t* gif_performance_monitor_get_stats(gif_performance_monitor_t* monitor) {
    if (!monitor) {
        return NULL;
    }
    return &monitor->stats;
}

void gif_performance_monitor_print_report(gif_performance_monitor_t* monitor) {
    if (!monitor) {
        return;
    }

    if (xSemaphoreTake(monitor->mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        const gif_performance_stats_t* stats = &monitor->stats;

        ESP_LOGI(TAG, "=== GIF Performance Report ===");
        ESP_LOGI(TAG, "Runtime: %.1f seconds", stats->total_runtime_ms / 1000.0f);

        // 帧率统计
        ESP_LOGI(TAG, "Frame Rate:");
        ESP_LOGI(TAG, "  Current: %.1f FPS", stats->current_fps);
        ESP_LOGI(TAG, "  Average: %.1f FPS", stats->average_fps);
        ESP_LOGI(TAG, "  Target: %.1f FPS", stats->target_fps);
        ESP_LOGI(TAG, "  Total frames: %lu", stats->frame_display_count);

        // 解码性能
        if (stats->decode_count > 0) {
            ESP_LOGI(TAG, "Decode Performance:");
            ESP_LOGI(TAG, "  Average: %.1f ms", stats->avg_decode_time_ms);
            ESP_LOGI(TAG, "  Min: %lu ms", stats->min_decode_time_ms);
            ESP_LOGI(TAG, "  Max: %lu ms", stats->max_decode_time_ms);
            ESP_LOGI(TAG, "  Total decodes: %lu", stats->decode_count);
        }

        // 缓存性能
        uint32_t total_cache_accesses = stats->cache_hits + stats->cache_misses;
        if (total_cache_accesses > 0) {
            ESP_LOGI(TAG, "Cache Performance:");
            ESP_LOGI(TAG, "  Hit rate: %.1f%%", stats->cache_hit_rate);
            ESP_LOGI(TAG, "  Hits: %lu", stats->cache_hits);
            ESP_LOGI(TAG, "  Misses: %lu", stats->cache_misses);
        }

        // 内存使用
        ESP_LOGI(TAG, "Memory Usage:");
        ESP_LOGI(TAG, "  Internal RAM: %zu KB (peak: %zu KB)",
                 stats->memory_used_internal / 1024, stats->memory_peak_internal / 1024);
        ESP_LOGI(TAG, "  PSRAM: %zu KB (peak: %zu KB)",
                 stats->memory_used_psram / 1024, stats->memory_peak_psram / 1024);
        ESP_LOGI(TAG, "  Free heap: %lu KB (min: %lu KB)",
                 stats->free_heap_size / 1024, stats->min_free_heap_size / 1024);

        // 性能评估
        bool target_met = gif_performance_monitor_check_target_performance(monitor);
        ESP_LOGI(TAG, "Performance Target: %s", target_met ? "✅ MET" : "❌ NOT MET");

        xSemaphoreGive(monitor->mutex);
    }
}

void gif_performance_monitor_reset(gif_performance_monitor_t* monitor) {
    if (!monitor) {
        return;
    }

    if (xSemaphoreTake(monitor->mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        float target_fps = monitor->stats.target_fps;
        memset(&monitor->stats, 0, sizeof(gif_performance_stats_t));
        monitor->stats.target_fps = target_fps;
        monitor->stats.start_time_ms = get_timestamp_ms();
        monitor->stats.min_decode_time_ms = UINT32_MAX;

        xSemaphoreGive(monitor->mutex);
    }

    ESP_LOGI(TAG, "Performance statistics reset");
}

bool gif_performance_monitor_check_target_performance(gif_performance_monitor_t* monitor) {
    if (!monitor) {
        return false;
    }

    const gif_performance_stats_t* stats = &monitor->stats;

    // 检查平均帧率是否达到目标的80%以上
    bool fps_ok = (stats->average_fps >= stats->target_fps * 0.8f);

    // 检查缓存命中率是否大于70%
    bool cache_ok = (stats->cache_hit_rate >= 70.0f) || (stats->cache_hits + stats->cache_misses == 0);

    // 检查平均解码时间是否合理 (小于帧间隔的50%)
    bool decode_ok = true;
    if (stats->target_fps > 0 && stats->decode_count > 0) {
        float max_decode_time = (1000.0f / stats->target_fps) * 0.5f;
        decode_ok = (stats->avg_decode_time_ms <= max_decode_time);
    }

    return fps_ok && cache_ok && decode_ok;
}
