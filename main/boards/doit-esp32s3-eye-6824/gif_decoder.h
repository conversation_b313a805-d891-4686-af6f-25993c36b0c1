#pragma once

#include <lvgl.h>
#include <stdint.h>
#include <esp_err.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief GIF解码器结构
 */
typedef struct {
    const lv_img_dsc_t* gif_source;    // GIF源数据
    uint32_t width;                    // 图像宽度
    uint32_t height;                   // 图像高度
    uint32_t frame_count;              // 总帧数
    uint32_t current_frame;            // 当前帧索引
    void* decoder_context;             // 解码器上下文 (内部使用)
} gif_decoder_t;

/**
 * @brief 创建GIF解码器
 * 
 * @param gif_source GIF源数据
 * @return gif_decoder_t* 解码器指针，失败返回NULL
 */
gif_decoder_t* gif_decoder_create(const lv_img_dsc_t* gif_source);

/**
 * @brief 销毁GIF解码器
 * 
 * @param decoder 解码器指针
 */
void gif_decoder_destroy(gif_decoder_t* decoder);

/**
 * @brief 获取GIF信息
 * 
 * @param decoder 解码器指针
 * @param width 输出宽度
 * @param height 输出高度
 * @param frame_count 输出帧数
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_decoder_get_info(gif_decoder_t* decoder, uint32_t* width, 
                              uint32_t* height, uint32_t* frame_count);

/**
 * @brief 解码指定帧到RGB565缓冲区
 * 
 * @param decoder 解码器指针
 * @param frame_index 帧索引
 * @param output_buffer 输出缓冲区 (RGB565格式)
 * @param buffer_size 缓冲区大小 (字节)
 * @param delay_ms 输出帧延迟时间 (毫秒)
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_decoder_decode_frame(gif_decoder_t* decoder, uint32_t frame_index,
                                  uint16_t* output_buffer, uint32_t buffer_size,
                                  uint32_t* delay_ms);

/**
 * @brief 重置解码器到第一帧
 * 
 * @param decoder 解码器指针
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_decoder_reset(gif_decoder_t* decoder);

#ifdef __cplusplus
}
#endif
