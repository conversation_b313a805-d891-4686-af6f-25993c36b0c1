#pragma once

/**
 * @file line_rendering_config.h
 * @brief 分行渲染配置文件
 * 
 * 定义了EyeGifDrawDisplay分行渲染的配置参数，
 * 用于优化内部RAM使用，减少DMA缓冲区占用。
 */

// 分行渲染配置
#define LINES_PER_BATCH_DEFAULT     8       // 默认每批渲染8行
#define LINES_PER_BATCH_MIN         1       // 最小1行
#define LINES_PER_BATCH_MAX         32      // 最大32行

// 内存使用计算宏
#define CALC_LINE_BUFFER_SIZE(width, lines) ((width) * (lines) * sizeof(uint16_t))
#define CALC_FRAME_BUFFER_SIZE(width, height) ((width) * (height) * sizeof(uint16_t))
#define CALC_CACHE_BUFFER_SIZE(width, height, frames) ((width) * (height) * (frames) * sizeof(uint16_t))

// 内存使用统计宏
#define MEMORY_SAVINGS_PER_SCREEN(width, height, lines) \
    (CALC_FRAME_BUFFER_SIZE(width, height) - CALC_LINE_BUFFER_SIZE(width, lines))

/**
 * @brief 分行渲染内存使用分析
 * 
 * 对于240x240屏幕：
 * 
 * 原方案（全帧DMA缓冲区）：
 * - 每屏内部RAM: 240 * 240 * 2 = 115,200 字节 (112.5 KB)
 * - 双屏总计: 225 KB 内部RAM
 * 
 * 新方案（8行分批渲染）：
 * - 每屏内部RAM: 240 * 8 * 2 = 3,840 字节 (3.75 KB)
 * - 双屏总计: 7.5 KB 内部RAM
 * - 节省内存: 217.5 KB 内部RAM (96.7% 节省)
 * 
 * 性能影响：
 * - 渲染次数: 240/8 = 30次 DMA传输 (vs 1次)
 * - 内存拷贝: 每帧需要30次小块拷贝 (PSRAM -> 内部RAM)
 * - DMA开销: 轻微增加，但内部RAM节省巨大
 */

// 不同行数配置的内存使用对比（240x240屏幕）
#define MEMORY_USAGE_1_LINE     (240 * 1 * 2)      // 480 字节
#define MEMORY_USAGE_2_LINES    (240 * 2 * 2)      // 960 字节
#define MEMORY_USAGE_4_LINES    (240 * 4 * 2)      // 1,920 字节
#define MEMORY_USAGE_8_LINES    (240 * 8 * 2)      // 3,840 字节
#define MEMORY_USAGE_16_LINES   (240 * 16 * 2)     // 7,680 字节
#define MEMORY_USAGE_32_LINES   (240 * 32 * 2)     // 15,360 字节

// 性能 vs 内存权衡建议
#define RECOMMENDED_LINES_LOW_RAM       4   // 低内存模式：1.9KB per screen
#define RECOMMENDED_LINES_BALANCED      8   // 平衡模式：3.8KB per screen  
#define RECOMMENDED_LINES_PERFORMANCE   16  // 性能模式：7.7KB per screen

/**
 * @brief 分行渲染流程说明
 * 
 * 1. 帧数据存储在PSRAM中（240x240x2 = 115KB）
 * 2. 分配小的内部RAM行缓冲区（240xNx2字节）
 * 3. 循环处理：
 *    a. 从PSRAM复制N行到内部RAM行缓冲区
 *    b. 使用DMA将行缓冲区渲染到LCD
 *    c. 重复直到整帧完成
 * 4. 总DMA传输次数：240/N 次
 * 
 * 优势：
 * - 大幅减少内部RAM占用（96%+ 节省）
 * - 保持DMA传输效率
 * - 支持任意大小的帧缓冲区
 * - 可配置的内存/性能权衡
 * 
 * 劣势：
 * - 增加DMA传输次数
 * - 轻微增加CPU开销（内存拷贝）
 * - 代码复杂度略有增加
 */

// 调试和监控宏
#ifdef DEBUG_LINE_RENDERING
#define LINE_RENDER_LOG(fmt, ...) ESP_LOGD("LineRender", fmt, ##__VA_ARGS__)
#else
#define LINE_RENDER_LOG(fmt, ...)
#endif

// 性能监控宏
#define MEASURE_RENDER_TIME_START() uint32_t render_start = esp_timer_get_time()
#define MEASURE_RENDER_TIME_END(screen_id) \
    do { \
        uint32_t render_time = esp_timer_get_time() - render_start; \
        ESP_LOGD("RenderPerf", "Screen %d render time: %lu us", screen_id, render_time); \
    } while(0)

// 内存使用验证宏
#define VERIFY_MEMORY_ALIGNMENT(ptr) \
    do { \
        if (((uintptr_t)(ptr) & 3) != 0) { \
            ESP_LOGW("MemAlign", "Buffer %p not 4-byte aligned", ptr); \
        } \
    } while(0)

// 配置验证宏
#define VALIDATE_LINES_PER_BATCH(lines) \
    ((lines) >= LINES_PER_BATCH_MIN && (lines) <= LINES_PER_BATCH_MAX)

/**
 * @brief 运行时配置结构
 */
typedef struct {
    uint32_t lines_per_batch;       // 每批渲染行数
    bool enable_performance_log;    // 是否启用性能日志
    bool enable_memory_monitor;     // 是否启用内存监控
    uint32_t render_timeout_ms;     // 渲染超时时间
} line_rendering_config_t;

// 默认配置
#define DEFAULT_LINE_RENDERING_CONFIG() { \
    .lines_per_batch = LINES_PER_BATCH_DEFAULT, \
    .enable_performance_log = false, \
    .enable_memory_monitor = true, \
    .render_timeout_ms = 100 \
}
