#pragma once

#include <lvgl.h>

// 声明src目录下的实际GIF资源
// 这些GIF资源已经通过工具转换为C数组格式

#ifdef __cplusplus
extern "C" {
#endif
    // 实际存在的GIF资源
    extern const lv_img_dsc_t staticstate;     // 静态/中性表情
    extern const lv_img_dsc_t happy;           // 开心表情
    extern const lv_img_dsc_t sad;             // 悲伤表情
    extern const lv_img_dsc_t anger;           // 愤怒表情
    extern const lv_img_dsc_t scare;           // 惊吓/惊讶表情
    extern const lv_img_dsc_t buxue;           // 不学/困惑/思考表情
    extern const lv_img_dsc_t heart_beat;
    extern const lv_img_dsc_t moving;
    extern const lv_img_dsc_t cute;
    extern const lv_img_dsc_t purple_eye_240_240;
    extern const lv_img_dsc_t purple_eye_240_240_ro;

#ifdef __cplusplus
}
#endif
