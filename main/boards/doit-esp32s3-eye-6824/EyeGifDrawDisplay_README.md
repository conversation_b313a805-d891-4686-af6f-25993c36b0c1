# EyeGifDrawDisplay - AnimatedGIF高性能显示类

## 概述

`EyeGifDrawDisplay` 是专为 doit-esp32s3-eye-6824 开发板设计的双屏 GIF 显示类，移除了 LVGL 依赖，使用开源 AnimatedGIF 库进行解码，并实现了10帧PSRAM缓存和DMA渲染功能。

## 主要特点

- ✅ **移除LVGL依赖**：不再依赖LVGL库，减少内存占用和复杂性
- ✅ **AnimatedGIF解码**：使用开源 AnimatedGIF 库进行高效GIF解码
- ✅ **10帧PSRAM缓存**：每屏预缓存10帧解码数据，显著提升播放流畅度
- ✅ **DMA渲染**：使用DMA兼容缓冲区进行高效数据传输
- ✅ **Turbo模式**：支持AnimatedGIF的Turbo加速模式（需要额外内存）
- ✅ **双屏独立控制**：支持两个屏幕显示不同的GIF动画
- ✅ **全屏显示**：240x240 GIF 动画全屏显示
- ✅ **智能内存管理**：PSRAM和内部RAM的智能分配策略

## 与原版本的区别

| 特性 | EyeGifDisplay (原版) | EyeGifDrawDisplay (新版) |
|------|---------------------|-------------------------|
| LVGL依赖 | ✅ 依赖LVGL | ❌ 移除LVGL依赖 |
| GIF解码 | LVGL GIF组件 | AnimatedGIF库 |
| 渲染方式 | LVGL渲染管道 | 直接LCD面板DMA渲染 |
| 帧缓存 | 无 | 10帧PSRAM缓存 |
| 加速模式 | 无 | Turbo模式支持 |
| 内存占用 | 较高（LVGL开销） | 中等（缓存+DMA缓冲区） |
| 性能 | 受LVGL限制 | 高性能直接硬件访问 |
| 流畅度 | 一般 | 极高（预缓存机制） |
| 复杂度 | 较高 | 中等 |

## 文件结构

```
main/boards/doit-esp32s3-eye-6824/
├── eye_gif_draw_display.h      # 头文件
├── eye_gif_draw_display.cc     # 实现文件
├── eye_gif_draw_example.cpp    # 使用示例
└── EyeGifDrawDisplay_README.md # 本文档
```

## 核心类结构

### 主要成员

```cpp
class EyeGifDrawDisplay : public Display {
private:
    // 帧缓存结构
    struct FrameCache {
        uint16_t* frame_data;        // RGB565帧数据
        uint32_t frame_index;        // 帧索引
        uint32_t delay_ms;           // 帧延迟
        bool is_valid;               // 是否有效
        uint32_t last_access;        // 最后访问时间
    };

    // GIF播放状态结构
    struct GifPlayState {
        AnimatedGIF* gif_decoder;    // AnimatedGIF解码器
        uint32_t current_frame;      // 当前帧
        uint32_t total_frames;       // 总帧数
        uint16_t* frame_buffer;      // RGB565帧缓冲区
        uint16_t* dma_buffer;        // DMA传输缓冲区
        uint8_t* turbo_buffer;       // Turbo加速缓冲区
        FrameCache frame_cache[10];  // 10帧缓存
        bool is_playing;             // 播放状态
        bool turbo_enabled;          // Turbo模式
        // ...
    };

    // 双屏状态
    GifPlayState gif_state1_;        // 屏幕1状态
    GifPlayState gif_state2_;        // 屏幕2状态

    // LCD面板句柄
    esp_lcd_panel_handle_t panel1_;  // 屏幕1面板
    esp_lcd_panel_handle_t panel2_;  // 屏幕2面板
};
```

### 核心方法

```cpp
// 基础接口
void SetEmotion(const char* emotion);                    // 设置表情（双屏同步）
void SetEmotionOnScreen(uint8_t screen_id, const char* emotion); // 单屏设置
void SetDualEmotion(const char* emotion1, const char* emotion2); // 双屏不同表情
void SetSyncEmotion(const char* emotion);                // 双屏同步表情

// 内部核心方法
esp_err_t LoadGifOnScreen(uint8_t screen_id, const lv_img_dsc_t* gif_resource);
esp_err_t UpdateGifFrame(uint8_t screen_id);
esp_err_t RenderFrameToScreen(uint8_t screen_id, const uint16_t* frame_buffer);

// 帧缓存管理
esp_err_t CacheFrame(GifPlayState* state, uint32_t frame_index, uint16_t* frame_data, uint32_t delay_ms);
FrameCache* GetCachedFrame(GifPlayState* state, uint32_t frame_index);
void InitializeFrameCache(GifPlayState* state);

// AnimatedGIF回调
static void AnimatedGIFDrawCallback(GIFDRAW* pDraw);
```

## 工作原理

### 1. 初始化流程

```
构造函数 → 获取LCD面板句柄 → 分配帧缓冲区 → 启动渲染任务
```

### 2. GIF播放流程

```
设置表情 → 创建AnimatedGIF解码器 → 检查帧缓存 → 解码/使用缓存帧 → DMA渲染到LCD面板 → 缓存新帧 → 循环播放
```

### 3. 帧缓存机制

```
解码新帧 → 存入PSRAM缓存 → 下次播放检查缓存 → 缓存命中直接使用 → LRU淘汰旧帧
```

### 4. 渲染任务

- 独立的FreeRTOS任务处理GIF渲染
- 使用队列接收渲染请求
- 智能帧缓存管理
- 定时更新GIF帧
- DMA优化的 `esp_lcd_panel_draw_bitmap` 渲染

## 使用示例

### 基本使用

```cpp
#include "eye_gif_draw_display.h"

// 创建显示器
DisplayFonts fonts;
EyeGifDrawDisplay display(240, 240, 0, 0, false, false, false, fonts);

// 设置表情（双屏同步）
display.SetEmotion("happy");

// 单屏设置
display.SetEmotionOnScreen(1, "happy");    // 左屏
display.SetEmotionOnScreen(2, "sad");      // 右屏

// 双屏不同表情
display.SetDualEmotion("purple_left", "purple_right");

// 双屏同步表情
display.SetSyncEmotion("heart_beat");
```

### 图标映射

```cpp
// 图标会自动映射到对应表情
display.SetIcon(FONT_AWESOME_DOWNLOAD);  // → "loading"
display.SetIcon(FONT_AWESOME_MUSIC);     // → "listening"
display.SetIcon(FONT_AWESOME_VOLUME_HIGH); // → "speaking"
```

## 内存管理

### 内存分配策略

- **帧缓冲区**：每屏 240×240×2 字节 RGB565 (PSRAM)
- **DMA缓冲区**：每屏 240×240×2 字节 RGB565 (内部RAM，DMA兼容)
- **10帧缓存**：每屏 10×240×240×2 字节 (PSRAM)
- **Turbo缓冲区**：每屏约 25KB (PSRAM，可选)
- **总内存占用**：约 2.5MB PSRAM + 230KB 内部RAM（双屏）

### 内存优化

```cpp
// 配置选项（在 gif_config.h 中）
#define GIF_USE_PSRAM_BUFFER    true    // 使用PSRAM存储帧缓冲区
#define GIF_SYNC_TASK_STACK     8192    // 渲染任务堆栈大小
#define GIF_SYNC_QUEUE_SIZE     10      // 渲染队列大小
```

## 性能特点

### 优势

- **高性能解码**：AnimatedGIF库优化的解码算法
- **智能缓存**：10帧PSRAM缓存显著提升播放流畅度
- **DMA加速**：硬件DMA传输减少CPU占用
- **Turbo模式**：可选的高速解码模式
- **直接硬件访问**：绕过LVGL渲染管道，直接操作LCD
- **内存优化**：智能的PSRAM和内部RAM分配策略
- **实时性**：独立渲染任务，不受其他UI组件影响
- **可控性**：完全控制GIF解码、缓存和渲染流程

### 性能指标

- **帧率**：目标20FPS（可通过 `GIF_LVGL_TIMER_PERIOD_MS` 调整）
- **缓存命中率**：循环播放时可达90%+
- **内存占用**：约2.5MB PSRAM + 230KB 内部RAM（双屏）
- **CPU占用**：显著降低（缓存命中时几乎无解码开销）
- **DMA传输**：硬件加速，减少CPU负担

## 调试功能

```cpp
// 显示配置信息
display.DebugDisplayInfo();

// 内存使用监控（自动）
// 会定期输出内存使用情况到日志
```

## 注意事项

1. **依赖项**：需要 `AnimatedGIF.h` 库和相关实现
2. **面板句柄**：依赖外部全局变量 `lcd_panel_eye` 和 `lcd_panel_eye2`
3. **线程安全**：使用互斥锁保护共享资源和帧缓存
4. **内存配置**：确保PSRAM可用且配置正确（需要约2.5MB PSRAM）
5. **DMA要求**：内部RAM缓冲区需要满足DMA对齐要求

## 故障排除

### 常见问题

1. **面板句柄无效**
   - 检查 `lcd_panel_eye` 和 `lcd_panel_eye2` 是否正确初始化

2. **内存分配失败**
   - 检查PSRAM配置
   - 调整 `GIF_USE_PSRAM_BUFFER` 设置

3. **GIF不显示**
   - 检查GIF资源是否有效
   - 查看解码器创建是否成功

4. **性能问题**
   - 调整 `GIF_LVGL_TIMER_PERIOD_MS`
   - 检查任务优先级配置

## 扩展开发

如需扩展功能，可以：

1. 添加新的表情映射到 `emotion_maps_` 数组
2. 实现自定义GIF解码器
3. 添加特效和过渡动画
4. 优化内存使用和性能
