#!/bin/bash

# EyeGifDisplay 迁移验证脚本
# 用于验证从旧的 EyeGifDisplay 到新的 EyeGifDrawDisplay 的迁移是否完成

echo "=== EyeGifDisplay 迁移验证 ==="
echo ""

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 验证计数器
PASS=0
FAIL=0

# 验证函数
verify_file_exists() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✓${NC} 文件存在: $1"
        ((PASS++))
    else
        echo -e "${RED}✗${NC} 文件缺失: $1"
        ((FAIL++))
    fi
}

verify_file_not_exists() {
    if [ ! -f "$1" ]; then
        echo -e "${GREEN}✓${NC} 旧文件已删除: $1"
        ((PASS++))
    else
        echo -e "${RED}✗${NC} 旧文件仍存在: $1"
        ((FAIL++))
    fi
}

verify_no_references() {
    local pattern="$1"
    local description="$2"
    local count=$(grep -r "$pattern" . --exclude-dir=doc --exclude="*.md" --exclude="*.sh" 2>/dev/null | wc -l)
    
    if [ "$count" -eq 0 ]; then
        echo -e "${GREEN}✓${NC} 无遗留引用: $description"
        ((PASS++))
    else
        echo -e "${RED}✗${NC} 发现 $count 个遗留引用: $description"
        grep -r "$pattern" . --exclude-dir=doc --exclude="*.md" --exclude="*.sh" 2>/dev/null | head -5
        ((FAIL++))
    fi
}

echo "1. 验证新文件是否存在..."
verify_file_exists "eye_gif_draw_display.h"
verify_file_exists "eye_gif_draw_display.cc"
verify_file_exists "eye_gif_draw_example.cpp"
verify_file_exists "line_rendering_config.h"
verify_file_exists "EyeGifDrawDisplay_README.md"
verify_file_exists "MIGRATION_SUMMARY.md"

echo ""
echo "2. 验证旧文件是否已删除..."
verify_file_not_exists "eye_gif_display.h"
verify_file_not_exists "eye_gif_display.cc"
verify_file_not_exists "gif_decoder.h"
verify_file_not_exists "gif_decoder.c"
verify_file_not_exists "gif_frame_cache.h"
verify_file_not_exists "gif_frame_cache.c"
verify_file_not_exists "gif_cache_test.h"
verify_file_not_exists "gif_cache_test.c"
verify_file_not_exists "gif_performance_monitor.h"
verify_file_not_exists "gif_performance_monitor.c"
verify_file_not_exists "gif_cache_example.cpp"

echo ""
echo "3. 验证代码中的引用是否已更新..."
verify_no_references "eye_gif_display\.h" "eye_gif_display.h 引用"
verify_no_references "EyeGifDisplay[^D]" "EyeGifDisplay 类引用"
verify_no_references "gif_decoder\.h" "gif_decoder.h 引用"
verify_no_references "gif_frame_cache\.h" "gif_frame_cache.h 引用"
verify_no_references "gif_performance_monitor\.h" "gif_performance_monitor.h 引用"

echo ""
echo "4. 验证新类的引用..."
if grep -r "EyeGifDrawDisplay" compact_wifi_board_lcd.cc >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC} compact_wifi_board_lcd.cc 已更新为使用 EyeGifDrawDisplay"
    ((PASS++))
else
    echo -e "${RED}✗${NC} compact_wifi_board_lcd.cc 未更新为使用 EyeGifDrawDisplay"
    ((FAIL++))
fi

if grep -r "eye_gif_draw_display\.h" compact_wifi_board_lcd.cc >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC} compact_wifi_board_lcd.cc 包含正确的头文件"
    ((PASS++))
else
    echo -e "${RED}✗${NC} compact_wifi_board_lcd.cc 未包含 eye_gif_draw_display.h"
    ((FAIL++))
fi

echo ""
echo "5. 验证 AnimatedGIF 组件..."
if [ -f "../../components/AnimateGIF_esp/include/AnimatedGIF.h" ]; then
    echo -e "${GREEN}✓${NC} AnimatedGIF 组件头文件存在"
    ((PASS++))
else
    echo -e "${RED}✗${NC} AnimatedGIF 组件头文件缺失"
    ((FAIL++))
fi

if [ -f "../../components/AnimateGIF_esp/AnimatedGIF.cpp" ]; then
    echo -e "${GREEN}✓${NC} AnimatedGIF 组件实现文件存在"
    ((PASS++))
else
    echo -e "${RED}✗${NC} AnimatedGIF 组件实现文件缺失"
    ((FAIL++))
fi

if grep -r "AnimatedGIF" test_compile.c >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC} test_compile.c 已更新为使用 AnimatedGIF"
    ((PASS++))
else
    echo -e "${RED}✗${NC} test_compile.c 未更新为使用 AnimatedGIF"
    ((FAIL++))
fi

echo ""
echo "=== 验证结果 ==="
echo -e "通过: ${GREEN}$PASS${NC}"
echo -e "失败: ${RED}$FAIL${NC}"

if [ $FAIL -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 迁移验证成功！${NC}"
    echo "所有检查都通过了，EyeGifDisplay 已成功迁移到 EyeGifDrawDisplay。"
    echo ""
    echo "下一步："
    echo "1. 编译项目: idf.py build"
    echo "2. 烧录测试: idf.py flash monitor"
    echo "3. 验证功能: 检查双屏GIF显示是否正常"
    exit 0
else
    echo ""
    echo -e "${RED}❌ 迁移验证失败！${NC}"
    echo "发现 $FAIL 个问题需要修复。"
    echo ""
    echo "建议："
    echo "1. 检查上述失败的项目"
    echo "2. 手动修复遗留的引用"
    echo "3. 重新运行此验证脚本"
    exit 1
fi
